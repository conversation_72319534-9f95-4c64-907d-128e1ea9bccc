/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */

/* ==========================================================================
   Normalize.css - Browser Reset Styles
   ========================================================================== */

/* Document
   ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */

html {
  line-height: 1.15; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/* Sections
   ========================================================================== */

/**
 * Remove the margin in all browsers.
 */

body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */

main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */

hr {
  box-sizing: content-box; /* 1 */
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/* Text-level semantics
   ========================================================================== */

/**
 * Remove the gray background on active links in IE 10.
 */

a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */

abbr[title] {
  border-bottom: none; /* 1 */
  text-decoration: underline; /* 2 */
  text-decoration: underline dotted; /* 2 */
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */

b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
 * Add the correct font size in all browsers.
 */

small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */

/**
 * Remove the border on images inside links in IE 10.
 */

img {
  border-style: none;
}

/* Forms
   ========================================================================== */

/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */

button,
input {
  /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */

button,
select {
  /* 1 */
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */

fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */

legend {
  box-sizing: border-box; /* 1 */
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  padding: 0; /* 3 */
  white-space: normal; /* 1 */
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */

progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */

textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */

[type="checkbox"],
[type="radio"] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */

[type="search"] {
  -webkit-appearance: textfield; /* 1 */
  appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/* Interactive
   ========================================================================== */

/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */

details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */

summary {
  display: list-item;
}

/* Misc
   ========================================================================== */

/**
 * Add the correct display in IE 10+.
 */

template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */

[hidden] {
  display: none;
}

/* ==========================================================================
   Bridge - Business Consulting Website Styles
   ========================================================================== */

/* ==========================================================================
   CSS Custom Properties (Variables)
   ========================================================================== */

:root {
  /* Colors */
  --primary-color: #f2d03b;
  --primary-hover: #e6c235;
  --secondary-color: #333745;
  --text-color: #666;
  --text-dark: #333;
  --text-light: #999;
  --light-gray: #f8f8f8;
  --medium-gray: #e8e8e8;
  --white: #ffffff;
  --black: #000000;
  --selection-color: #f2d03b; /* Yellow selection color */
  --selection-bg: rgba(242, 208, 59, 0.3);

  /* Typography */
  --font-family: "Open Sans", sans-serif;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Layout */
  --container-max-width: 70rem;
  --border-radius: 5px;
  --border-radius-small: 3px;

  /* Transitions */
  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.4s ease;

  /* Shadows */
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 5px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* ==========================================================================
   Text Selection Styles (Yellow Selection)
   ========================================================================== */

::selection {
  background-color: var(--selection-bg);
  color: var(--text-dark);
}

::-moz-selection {
  background-color: var(--selection-bg);
  color: var(--text-dark);
}

/* ==========================================================================
   Reset and Base Styles
   ========================================================================== */

html {
  box-sizing: border-box;
  scroll-behavior: smooth;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Reset margins and paddings */
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
li,
figure,
figcaption,
blockquote,
dl,
dd {
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--white);
}

/* Links */
a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition);
}

a:hover {
  text-decoration: none;
}

/* Lists */
ul,
ol {
  list-style: none;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Buttons */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
}

/* ==========================================================================
   Layout Components
   ========================================================================== */

.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1rem;
}

/* ==========================================================================
   Header Styles
   ========================================================================== */

/* Header Top Section */
.header-top-wrapper {
  background-color: var(--secondary-color);
}

.header-info {
  color: var(--white);
  padding: 11px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.7rem;
  font-weight: var(--font-weight-light);
}

.header-info-contact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-info__phone,
.header-info__email {
  margin: 0;
}

.header-info__email {
  color: var(--primary-color);
}

.header-info-social {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-info__text {
  margin: 0;
}

.header-info__icon {
  display: flex;
  gap: 0.5rem;
}

.header-info__icon i {
  transition: var(--transition);
}

.header-info__icon i:hover {
  color: var(--primary-color);
  cursor: pointer;
}

/* Navigation Styles */
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.nav-content {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-logo {
  max-width: 8rem;
}

.nav-logo__image {
  width: 100%;
}

.menu {
  display: flex;
  align-items: center;
}

.menu__link {
  display: inline-block;
  padding: 30px 20px;
  color: var(--text-light);
  font-size: 0.9rem;
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: var(--transition);
}

.menu__text {
  border-bottom: 2px solid transparent;
  transition: var(--transition-slow);
  padding-bottom: 2px;
}

.menu__link:hover .menu__text,
.menu__link.active .menu__text {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.nav-search {
  padding: 0.5rem;
  cursor: pointer;
  transition: var(--transition);
}

.nav-search i {
  color: var(--text-light);
  font-size: 1.1rem;
  transition: var(--transition);
}

.nav-search:hover i {
  color: var(--secondary-color);
}

/* Header Responsive Styles */
@media (max-width: 768px) {
  .header-info {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    padding: 15px 0;
  }

  .header-info-contact,
  .header-info-social {
    justify-content: center;
  }

  .nav {
    flex-direction: column;
    gap: 20px;
    padding: 1.5rem 0;
  }

  .nav-content {
    flex-direction: column;
    gap: 1rem;
  }

  .menu {
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }

  .menu__link {
    padding: 15px 10px;
    font-size: 0.8rem;
  }
}

/* ==========================================================================
   Hero Slider Section
   ========================================================================== */

.hero-slider {
  position: relative;
  width: 100%;
  height: 508px;
  overflow: hidden;
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  display: flex;
  align-items: center;
}

.slide.active {
  opacity: 1;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.slide-content {
  position: relative;
  z-index: 2;
  max-width: 500px;
  width: 100%;
}

/* Slide Content Positioning */
.slide:nth-child(1) {
  justify-content: flex-start;
}

.slide:nth-child(1) .slide-content {
  text-align: left;
  margin-left: 226px;
  margin-right: auto;
}

.slide:nth-child(2) {
  justify-content: flex-end;
}

.slide:nth-child(2) .slide-content {
  text-align: right;
  margin-left: auto;
  margin-right: 300px;
}

/* Slide Typography */
.slide-title {
  font-size: 3rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: 2rem;
  position: relative;
  color: var(--text-dark);
  letter-spacing: 1px;
}

.slide-title::before {
  content: "";
  background-color: var(--primary-color);
  width: 42px;
  height: 8px;
  position: absolute;
  top: 52px;
}

.slide-description {
  line-height: 1.8;
  margin-bottom: 3rem;
  font-weight: var(--font-weight-light);
  max-width: 450px;
  opacity: 0.9;
  color: #5c5c5c;
  font-size: 1rem;
}

/* Description Alignment */
.slide:nth-child(1) .slide-description {
  margin-left: 0;
  margin-right: auto;
}

.slide:nth-child(2) .slide-description {
  margin-left: auto;
  margin-right: 0;
}

/* Slide Buttons */
.slide-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.slide:nth-child(1) .slide-buttons {
  justify-content: flex-start;
}

.slide:nth-child(2) .slide-buttons {
  justify-content: flex-end;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 15px 35px;
  text-decoration: none;
  text-transform: uppercase;
  font-weight: var(--font-weight-normal);
  font-size: 0.8rem;
  letter-spacing: 2px;
  border: none;
  border-radius: var(--border-radius);
  transition: var(--transition-slow);
  cursor: pointer;
  font-family: var(--font-family);
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-dark);
}

.btn-primary:hover {
  background-color: transparent;
  color: var(--black);
  box-shadow: var(--shadow-light);
}

.btn-secondary {
  background-color: #2f2f2f;
  color: var(--white);
}

.btn-secondary:hover {
  background-color: var(--white);
  color: var(--text-dark);
  box-shadow: var(--shadow-light);
}

/* Slider Navigation */
.slider-navigation {
  position: absolute;
  bottom: 50px;
  left: 0;
  right: 0;
  z-index: 3;
}

.nav-dots {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.nav-dot {
  width: 10px;
  height: 10px;
  background-color: rgba(148, 148, 148, 0.7);
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.nav-dot:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

.nav-dot.active {
  background-color: var(--white);
}

/* Slider Responsive Styles */
@media (max-width: 1024px) {
  .slide:nth-child(1) .slide-content {
    margin-left: 80px;
  }

  .slide:nth-child(2) .slide-content {
    margin-right: 80px;
  }

  .slide-title {
    font-size: 2.8rem;
  }
}

@media (max-width: 768px) {
  .hero-slider {
    height: 400px;
  }

  .slide:nth-child(1),
  .slide:nth-child(2) {
    justify-content: center;
  }

  .slide:nth-child(1) .slide-content,
  .slide:nth-child(2) .slide-content {
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    max-width: 90%;
  }

  .slide:nth-child(1) .slide-description,
  .slide:nth-child(2) .slide-description {
    margin-left: auto;
    margin-right: auto;
  }

  .slide-title {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    letter-spacing: 1px;
  }

  .slide-title::before {
    left: 50%;
    transform: translateX(-50%);
  }

  .slide-description {
    font-size: 0.95rem;
    margin-bottom: 2.5rem;
    max-width: 100%;
  }

  .slide-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .slide:nth-child(1) .slide-buttons,
  .slide:nth-child(2) .slide-buttons {
    justify-content: center;
  }

  .btn {
    padding: 12px 30px;
    font-size: 0.75rem;
  }

  .nav-dots {
    gap: 1.5rem;
  }

  .slider-navigation {
    bottom: 30px;
  }
}

@media (max-width: 480px) {
  .hero-slider {
    height: 350px;
  }

  .slide-title {
    font-size: 2rem;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
  }

  .slide-description {
    font-size: 0.9rem;
    margin-bottom: 2rem;
    line-height: 1.6;
  }

  .slide:nth-child(1) .slide-content,
  .slide:nth-child(2) .slide-content {
    max-width: 95%;
    padding: 0 20px;
  }

  .btn {
    padding: 10px 25px;
    font-size: 0.7rem;
    letter-spacing: 1px;
  }

  .nav-dots {
    gap: 1rem;
  }

  .slider-navigation {
    bottom: 25px;
  }
}

/* ==========================================================================
   Services Section
   ========================================================================== */

.services-section {
  padding: 80px 0;
  background-color: var(--white);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

.service-item {
  text-align: center;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: var(--transition);
}

.service-item:hover {
  transform: translateY(-5px);
}

.service-icon {
  margin-bottom: 25px;
  background-color: var(--light-gray);
  border-radius: 50%;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.service-icon i {
  font-size: 2rem;
  color: var(--text-dark);
  transition: var(--transition);
}

.service-item:hover .service-icon {
  background-color: var(--primary-color);
}

.service-item:hover .service-icon i {
  color: var(--white);
}

.service-title {
  font-size: 1rem;
  font-weight: var(--font-weight-light);
  margin-bottom: 15px;
  color: var(--black);
  letter-spacing: 1px;
  text-transform: uppercase;
}

.service-description {
  font-size: 0.85rem;
  line-height: 1.9;
  color: var(--text-light);
  font-weight: var(--font-weight-light);
  max-width: 200px;
}

/* Services Responsive Styles */
@media (max-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .services-section {
    padding: 60px 0;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .service-item {
    padding: 30px 15px;
  }

  .service-icon {
    width: 80px;
    height: 80px;
  }

  .service-icon i {
    font-size: 1.8rem;
  }

  .service-title {
    font-size: 1.1rem;
  }

  .service-description {
    max-width: 100%;
  }
}

/* ==========================================================================
   About Section
   ========================================================================== */

.about-section {
  padding: 80px 0;
  background-color: var(--white);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.about-text {
  padding-right: 40px;
}

.about-title {
  margin-bottom: 30px;
  color: var(--secondary-color);
  font-size: 1.125rem;
  font-weight: var(--font-weight-bold);
  letter-spacing: 0.5px;
  text-transform: uppercase;
  position: relative;
}

.about-title::before {
  content: "";
  background-color: var(--primary-color);
  width: 42px;
  height: 8px;
  position: absolute;
  top: 42px;
  left: 0;
}

.about-description {
  font-size: 1rem;
  line-height: 1.8;
  color: var(--text-color);
  margin-bottom: 20px;
  font-weight: var(--font-weight-light);
}

.about-image {
  text-align: center;
}

.businessman-img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  transition: var(--transition);
}

.businessman-img:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-medium);
}

/* About Responsive Styles */
@media (max-width: 768px) {
  .about-section {
    padding: 60px 0;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .about-text {
    padding-right: 0;
    text-align: center;
  }

  .about-title {
    font-size: 1.5rem;
  }

  .about-title::before {
    left: 50%;
    transform: translateX(-50%);
  }
}

/* ==========================================================================
   Skills Section
   ========================================================================== */

.Skills-section {
  padding: 80px 0;
  background-color: var(--white);
}

.Skills-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.customers-title,
.Skills-title {
  margin-bottom: 40px;
  position: relative;
  color: var(--secondary-color);
  font-size: 1.125rem;
  font-weight: var(--font-weight-bold);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.customers-title::before,
.Skills-title::before {
  content: "";
  background-color: var(--primary-color);
  width: 42px;
  height: 8px;
  position: absolute;
  top: 43px;
  left: 0;
}

.skills-description {
  font-size: 1rem;
  line-height: 1.8;
  color: var(--text-color);
  margin-bottom: 40px;
  font-weight: var(--font-weight-light);
}

.skills-bars {
  display: flex;
  justify-content: space-between;
  gap: 30px;
  align-items: flex-end;
  height: 300px;
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
}

.skill-info {
  margin-top: 20px;
  order: 2;
  text-align: center;
}

.skill-name {
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  color: var(--secondary-color);
  margin-bottom: 5px;
}

.skill-percentage {
  font-size: 0.9rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
}

.skill-bar {
  width: 100px;
  height: 200px;
  background-color: var(--medium-gray);
  border-radius: var(--border-radius-small);
  overflow: hidden;
  display: flex;
  align-items: flex-end;
  order: 1;
  position: relative;
}

.skill-progress {
  width: 100%;
  border-radius: var(--border-radius-small);
  transition: height 1.5s ease-in-out;
  height: 0;
  position: relative;
}

/* Individual skill progress colors and heights */
.skill-progress[data-percentage="39"] {
  height: 39%;
  background-color: var(--primary-color);
}

.skill-progress[data-percentage="54"] {
  height: 54%;
  background-color: #888888;
}

.skill-progress[data-percentage="78"] {
  height: 78%;
  background-color: var(--secondary-color);
}
/* ==========================================================================
   Accordion Component
   ========================================================================== */

.accordion {
  background: transparent;
  color: var(--text-dark);
  cursor: pointer;
  padding: 10px 17px;
  border-radius: var(--border-radius-small);
  font-size: 1.25rem;
  font-weight: var(--font-weight-bold);
  border: none;
  outline: none;
  transition: var(--transition);
  margin-bottom: 0.5rem;
}

.accordion.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.accordion:hover {
  background-color: var(--primary-hover);
  color: var(--white);
}

.accordion-title {
  margin-left: 17px;
  color: inherit;
  font-weight: var(--font-weight-medium);
  transition: var(--transition);
}

.panel {
  padding: 0 18px;
  color: var(--text-light);
  max-width: 600px;
  margin-left: 43px;
  background-color: var(--white);
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  transition: var(--transition-slow);
  border-radius: var(--border-radius-small);
}

.panel.open {
  max-height: 200px;
  opacity: 1;
  padding: 15px 18px;
}

.panel p {
  line-height: 1.7;
  font-size: 0.9rem;
  margin: 0;
}

/* Skills Section Responsive Styles */
@media (max-width: 768px) {
  .Skills-section {
    padding: 60px 0;
  }

  .Skills-content {
    grid-template-columns: 1fr;
    gap: 50px;
  }

  .customers-title::before,
  .Skills-title::before {
    left: 50%;
    transform: translateX(-50%);
  }

  .skills-bars {
    justify-content: center;
    gap: 20px;
    height: 250px;
  }

  .skill-bar {
    width: 80px;
    height: 150px;
  }

  .panel {
    max-width: 100%;
    margin-left: 0;
  }

  .accordion-title {
    margin-left: 10px;
  }
}

/* ==========================================================================
   Footer Section
   ========================================================================== */

.footer {
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 80px 0 0;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 60px;
  margin-bottom: 50px;
}

.footer-brand {
  padding-right: 40px;
}

.footer-logo {
  max-width: 150px;
  height: auto;
  margin-bottom: 25px;
}

.footer-description {
  font-size: 0.9rem;
  line-height: 1.7;
  color: #ccc;
  margin-bottom: 30px;
  font-weight: var(--font-weight-light);
}

.footer-social {
  display: flex;
  gap: 15px;
}

.social-link {
  width: 40px;
  height: 40px;
  background-color: #6d6d6d;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--secondary-color);
  text-decoration: none;
  transition: var(--transition);
}

.social-link:hover {
  background-color: var(--white);
  transform: translateY(-3px);
  box-shadow: var(--shadow-light);
}

.footer-title {
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  margin-bottom: 25px;
  color: var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.footer-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-list li {
  font-size: 0.9rem;
  line-height: 1.8;
  color: #ccc;
  margin-bottom: 8px;
  font-weight: var(--font-weight-light);
  transition: var(--transition);
}

.footer-list li:hover {
  color: var(--primary-color);
  cursor: pointer;
}

.newsletter-form {
  display: flex;
  gap: 15px;
  flex-direction: column;
}

.newsletter-input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid #555;
  background-color: #444;
  color: var(--white);
  border-radius: var(--border-radius-small);
  font-size: 0.9rem;
  font-family: var(--font-family);
  transition: var(--transition);
}

.newsletter-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: #555;
}

.newsletter-input::placeholder {
  color: #999;
}

.newsletter-btn {
  align-self: center;
  padding: 12px 25px;
  background-color: var(--primary-color);
  border: none;
  border-radius: var(--border-radius-small);
  color: var(--secondary-color);
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: var(--transition);
}

.newsletter-btn:hover {
  background-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.footer-bottom {
  border-top: 1px solid #555;
  padding: 30px 0;
  text-align: center;
}

.copyright {
  font-size: 0.8rem;
  color: #999;
  margin: 0;
  font-weight: var(--font-weight-light);
}

.copyright a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);
}

.copyright a:hover {
  text-decoration: underline;
  color: var(--primary-hover);
}

/* Newsletter Form Validation Styles */
.newsletter-input.valid {
  border-color: #28a745;
  background-color: #f8fff9;
}

.newsletter-input.invalid {
  border-color: #dc3545;
  background-color: #fff8f8;
}

.newsletter-message {
  margin-top: 10px;
  padding: 10px 15px;
  border-radius: var(--border-radius-small);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium);
}

.newsletter-message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.newsletter-message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.newsletter-message.info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Footer Responsive Styles */
@media (max-width: 768px) {
  .footer {
    padding: 60px 0 0;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .footer-brand {
    padding-right: 0;
  }

  .newsletter-form {
    align-items: center;
  }

  .newsletter-input {
    max-width: 300px;
  }

  .newsletter-btn {
    width: fit-content;
  }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
.btn:focus,
.nav-search:focus,
.menu__link:focus,
.nav-dot:focus,
.accordion:focus,
.newsletter-input:focus,
.newsletter-btn:focus,
.social-link:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Smooth transitions for interactive elements */
.service-item,
.btn,
.nav-search,
.menu__link,
.nav-dot,
.accordion,
.social-link {
  transition: var(--transition);
}

/* Print styles */
@media print {
  .header-top,
  .nav-search,
  .slider-navigation,
  .footer {
    display: none;
  }

  .slide {
    position: static;
    opacity: 1;
    background: none !important;
  }

  .slide-content {
    color: #000;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }
}
