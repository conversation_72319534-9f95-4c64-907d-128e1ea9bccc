/**
 * Bridge Business Consulting Website
 * Main JavaScript functionality
 */

document.addEventListener("DOMContentLoaded", function () {
  "use strict";

  // ==========================================================================
  // Hero Slider Module
  // ==========================================================================

  const SliderModule = {
    // Configuration
    config: {
      slideIntervalTime: 5000, // 5 seconds
      transitionDuration: 1000 // 1 second
    },

    // DOM elements
    elements: {
      slides: document.querySelectorAll(".slide"),
      navDots: document.querySelectorAll(".nav-dot"),
      sliderContainer: document.querySelector(".hero-slider")
    },

    // State
    state: {
      currentSlide: 0,
      slideInterval: null,
      isPlaying: false
    },

    // Initialize slider
    init() {
      if (this.elements.slides.length === 0 || this.elements.navDots.length === 0) {
        console.warn("Slider elements not found");
        return;
      }

      this.setupBackgroundImages();
      this.bindEvents();
      this.showSlide(0);
      this.startAutoPlay();

      console.log("Slider initialized successfully");
    },

    // Set background images for slides
    setupBackgroundImages() {
      this.elements.slides.forEach((slide) => {
        const bgImage = slide.getAttribute("data-bg");
        if (bgImage) {
          slide.style.backgroundImage = `url('${bgImage}')`;
        }
      });
    },

    // Show specific slide
    showSlide(index) {
      if (index < 0 || index >= this.elements.slides.length) {
        console.error("Invalid slide index:", index);
        return;
      }

      // Update ARIA attributes for accessibility
      this.elements.slides.forEach((slide, i) => {
        slide.classList.toggle("active", i === index);
        slide.setAttribute("aria-hidden", i !== index);
      });

      this.elements.navDots.forEach((dot, i) => {
        dot.classList.toggle("active", i === index);
        dot.setAttribute("aria-selected", i === index);
      });

      this.state.currentSlide = index;
    },

    // Go to next slide
    nextSlide() {
      const next = (this.state.currentSlide + 1) % this.elements.slides.length;
      this.showSlide(next);
    },

    // Go to previous slide
    prevSlide() {
      const prev = (this.state.currentSlide - 1 + this.elements.slides.length) % this.elements.slides.length;
      this.showSlide(prev);
    },

    // Start auto-play
    startAutoPlay() {
      if (this.state.isPlaying) return;

      this.state.slideInterval = setInterval(() => {
        this.nextSlide();
      }, this.config.slideIntervalTime);

      this.state.isPlaying = true;
    },

    // Stop auto-play
    stopAutoPlay() {
      if (this.state.slideInterval) {
        clearInterval(this.state.slideInterval);
        this.state.slideInterval = null;
      }
      this.state.isPlaying = false;
    },

    // Bind event listeners
    bindEvents() {
      // Navigation dots click events
      this.elements.navDots.forEach((dot, index) => {
        dot.addEventListener("click", (e) => {
          e.preventDefault();
          this.stopAutoPlay();
          this.showSlide(index);
          this.startAutoPlay();
        });
      });

      // Pause on hover
      if (this.elements.sliderContainer) {
        this.elements.sliderContainer.addEventListener("mouseenter", () => {
          this.stopAutoPlay();
        });

        this.elements.sliderContainer.addEventListener("mouseleave", () => {
          this.startAutoPlay();
        });
      }

      // Keyboard navigation
      document.addEventListener("keydown", (e) => {
        if (e.target.closest(".hero-slider")) {
          switch (e.key) {
            case "ArrowLeft":
              e.preventDefault();
              this.stopAutoPlay();
              this.prevSlide();
              this.startAutoPlay();
              break;
            case "ArrowRight":
              e.preventDefault();
              this.stopAutoPlay();
              this.nextSlide();
              this.startAutoPlay();
              break;
          }
        }
      });
    }
  };

  // Initialize slider
  SliderModule.init();

  // ==========================================================================
  // Navigation Module
  // ==========================================================================

  const NavigationModule = {
    // Initialize navigation functionality
    init() {
      this.setupSmoothScrolling();
      this.setupActiveNavigation();
      console.log("Navigation initialized successfully");
    },

    // Setup smooth scrolling for navigation links
    setupSmoothScrolling() {
      const navLinks = document.querySelectorAll(".menu__link");

      navLinks.forEach((link) => {
        link.addEventListener("click", function (e) {
          const href = this.getAttribute("href");

          if (href && href.startsWith("#")) {
            e.preventDefault();
            const target = document.querySelector(href);

            if (target) {
              // Remove active class from all nav items
              navLinks.forEach(navLink => {
                navLink.closest('.menu__item').classList.remove('active');
                navLink.removeAttribute('aria-current');
              });

              // Add active class to clicked nav item
              this.closest('.menu__item').classList.add('active');
              this.setAttribute('aria-current', 'page');

              // Smooth scroll to target
              target.scrollIntoView({
                behavior: "smooth",
                block: "start",
              });
            }
          }
        });
      });
    },

    // Setup active navigation based on scroll position
    setupActiveNavigation() {
      const sections = document.querySelectorAll('section[id]');
      const navLinks = document.querySelectorAll('.menu__link[href^="#"]');

      if (sections.length === 0) return;

      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const id = entry.target.getAttribute('id');
            const activeLink = document.querySelector(`.menu__link[href="#${id}"]`);

            if (activeLink) {
              // Remove active from all
              navLinks.forEach(link => {
                link.closest('.menu__item').classList.remove('active');
                link.removeAttribute('aria-current');
              });

              // Add active to current
              activeLink.closest('.menu__item').classList.add('active');
              activeLink.setAttribute('aria-current', 'page');
            }
          }
        });
      }, {
        threshold: 0.3,
        rootMargin: '-20% 0px -20% 0px'
      });

      sections.forEach(section => observer.observe(section));
    }
  };

  // ==========================================================================
  // Newsletter Module
  // ==========================================================================

  const NewsletterModule = {
    // Initialize newsletter functionality
    init() {
      const form = document.querySelector(".newsletter-form");
      if (!form) {
        console.warn("Newsletter form not found");
        return;
      }

      this.bindEvents(form);
      console.log("Newsletter module initialized successfully");
    },

    // Bind form events
    bindEvents(form) {
      form.addEventListener("submit", (e) => {
        e.preventDefault();
        this.handleSubmit(form);
      });

      // Real-time validation
      const inputs = form.querySelectorAll('.newsletter-input');
      inputs.forEach(input => {
        input.addEventListener('blur', () => {
          this.validateInput(input);
        });
      });
    },

    // Handle form submission
    handleSubmit(form) {
      const inputs = form.querySelectorAll('.newsletter-input');
      let isValid = true;

      // Validate all inputs
      inputs.forEach(input => {
        if (!this.validateInput(input)) {
          isValid = false;
        }
      });

      if (isValid) {
        // Simulate API call
        this.showMessage("Thank you for subscribing to our newsletter!", "success");
        form.reset();
      }
    },

    // Validate individual input
    validateInput(input) {
      const value = input.value.trim();
      const type = input.type;
      let isValid = true;
      let message = "";

      if (!value) {
        isValid = false;
        message = "This field is required";
      } else if (type === "email" && !this.isValidEmail(value)) {
        isValid = false;
        message = "Please enter a valid email address";
      }

      this.showInputValidation(input, isValid, message);
      return isValid;
    },

    // Email validation
    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    // Show input validation state
    showInputValidation(input, isValid, message) {
      // Remove existing validation classes
      input.classList.remove('valid', 'invalid');

      // Add appropriate class
      input.classList.add(isValid ? 'valid' : 'invalid');

      // Update ARIA attributes
      input.setAttribute('aria-invalid', !isValid);

      if (!isValid && message) {
        console.warn(`Validation error for ${input.name || input.type}: ${message}`);
      }
    },

    // Show success/error messages
    showMessage(text, type = "info") {
      // Create or update message element
      let messageEl = document.querySelector('.newsletter-message');

      if (!messageEl) {
        messageEl = document.createElement('div');
        messageEl.className = 'newsletter-message';
        const form = document.querySelector('.newsletter-form');
        form.appendChild(messageEl);
      }

      messageEl.textContent = text;
      messageEl.className = `newsletter-message ${type}`;

      // Auto-hide after 5 seconds
      setTimeout(() => {
        if (messageEl.parentNode) {
          messageEl.remove();
        }
      }, 5000);
    }
  };

  // ==========================================================================
  // Accordion Module
  // ==========================================================================

  const AccordionModule = {
    // Initialize accordion functionality
    init() {
      const accordions = document.querySelectorAll('.accordion');
      const panels = document.querySelectorAll('.panel');

      if (accordions.length === 0) {
        console.warn("No accordion elements found");
        return;
      }

      this.bindEvents(accordions, panels);
      console.log("Accordion module initialized successfully");
    },

    // Bind accordion events
    bindEvents(accordions, panels) {
      accordions.forEach((accordion, index) => {
        accordion.addEventListener('click', (e) => {
          e.preventDefault();
          this.toggleAccordion(accordion, panels[index], accordions, panels);
        });

        // Keyboard accessibility
        accordion.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.toggleAccordion(accordion, panels[index], accordions, panels);
          }
        });
      });
    },

    // Toggle accordion state
    toggleAccordion(clickedAccordion, clickedPanel, allAccordions, allPanels) {
      const isActive = clickedAccordion.classList.contains('active');

      // Close all accordions (exclusive behavior)
      this.closeAllAccordions(allAccordions, allPanels);

      // If clicked accordion wasn't active, open it
      if (!isActive) {
        this.openAccordion(clickedAccordion, clickedPanel);
      }
    },

    // Close all accordions
    closeAllAccordions(accordions, panels) {
      accordions.forEach((accordion, index) => {
        accordion.classList.remove('active');
        accordion.innerHTML = '+';
        accordion.setAttribute('aria-expanded', 'false');

        if (panels[index]) {
          panels[index].classList.remove('open');
          panels[index].setAttribute('aria-hidden', 'true');
        }
      });
    },

    // Open specific accordion
    openAccordion(accordion, panel) {
      accordion.classList.add('active');
      accordion.innerHTML = '-';
      accordion.setAttribute('aria-expanded', 'true');

      if (panel) {
        panel.classList.add('open');
        panel.setAttribute('aria-hidden', 'false');
      }
    }
  };

  // ==========================================================================
  // Initialize All Modules
  // ==========================================================================

  NavigationModule.init();
  NewsletterModule.init();
  AccordionModule.init();

  console.log("All modules initialized successfully");
});
